using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EnvelopeService.Core.Models
{
    /// <summary>
    /// Room construction model following EnvelopeService pattern
    /// Mirrors Hot/HouseFileLibrary/Components/RoomComponents/RoomConstruction.cs
    /// </summary>
    public class RoomConstruction
    {
        public Guid Id { get; set; }
        
        public Guid RoomId { get; set; }

        // Room type - using resource approach
        public RoomTypes Type { get; set; } = RoomTypes.Kitchen;

        // Floor level - using resource approach
        public RoomFloors Floor { get; set; } = RoomFloors.GroundFloor;

        // Foundation below - using CodeAndText approach like original
        public CodeAndText FoundationBelow { get; set; } = new CodeAndText();

        // Navigation property back to parent
        [ForeignKey("RoomId")]
        public Room Room { get; set; } = null!;

        public RoomConstruction()
        {
            SetDefaults();
        }

        public RoomConstruction(RoomConstruction toCopy)
        {
            Id = toCopy.Id;
            RoomId = toCopy.RoomId;
            Type = new RoomTypes(toCopy.Type);
            Floor = new RoomFloors(toCopy.Floor);
            FoundationBelow = new CodeAndText(toCopy.FoundationBelow);
        }

        public void SetDefaults()
        {
            Type = RoomTypes.Kitchen;
            Floor = RoomFloors.GroundFloor;
            FoundationBelow = new CodeAndText();
        }
    }
}
