using System.Collections.Generic;

namespace EnvelopeService.Core.Models
{
    /// <summary>
    /// Room types resource following H2kResources pattern
    /// Mirrors Hot/HouseFileLibrary/H2kResources/H2kResources.cs RoomTypes
    /// </summary>
    public class RoomTypes : ResourceList
    {
        public static readonly RoomTypes Kitchen = new RoomTypes("1", "Kitchen", "Cuisine");
        public static readonly RoomTypes LivingRoom = new RoomTypes("2", "Living Room", "Salon");
        public static readonly RoomTypes DiningRoom = new RoomTypes("3", "Dining Room", "Salle à Manger");
        public static readonly RoomTypes Bedroom = new RoomTypes("4", "Bedroom", "Chambre");
        public static readonly RoomTypes Bathroom = new RoomTypes("5", "Bathroom", "Salle de Bain");
        public static readonly RoomTypes UtilityRoom = new RoomTypes("6", "Utility Room", "Pièce Utilitaire");
        public static readonly RoomTypes Other = new RoomTypes("7", "Other", "Autre");

        public static List<RoomTypes> All => new List<RoomTypes>
        {
            Kitchen,
            LivingRoom,
            DiningRoom,
            Bedroom,
            Bathroom,
            UtilityRoom,
            Other
        };

        // Private constructor
        private RoomTypes(string code, string englishText, string frenchText, bool isUserSpecified = false)
            : base(code, englishText, frenchText, isUserSpecified)
        {
        }

        // Copy constructor
        public RoomTypes(RoomTypes toCopy) : base(toCopy)
        {
        }

        // Empty constructor needed for Entity Framework
        public RoomTypes() : base() { }
    }
}
