using System.Collections.Generic;

namespace EnvelopeService.Core.Models
{
    /// <summary>
    /// Room floors resource following H2kResources pattern
    /// Mirrors Hot/HouseFileLibrary/H2kResources/H2kResources.cs RoomFloors
    /// </summary>
    public class RoomFloors : ResourceList
    {
        public static readonly RoomFloors GroundFloor = new RoomFloors("1", "Ground Floor", "Rez-de-Chaussée");
        public static readonly RoomFloors SecondFloor = new RoomFloors("2", "Second Floor", "Deuxième Étage");
        public static readonly RoomFloors ThirdFloor = new RoomFloors("3", "Third Floor", "Troisième Étage");

        public static List<RoomFloors> All => new List<RoomFloors>
        {
            GroundFloor,
            SecondFloor,
            ThirdFloor
        };

        // Private constructor
        private RoomFloors(string code, string englishText, string frenchText, bool isUserSpecified = false)
            : base(code, englishText, frenchText, isUserSpecified)
        {
        }

        // Copy constructor
        public RoomFloors(RoomFloors toCopy) : base(toCopy)
        {
        }

        // Empty constructor needed for Entity Framework
        public RoomFloors() : base() { }
    }
}
