using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EnvelopeService.Core.Models
{
    /// <summary>
    /// Room measurements model following EnvelopeService pattern
    /// Mirrors Hot/HouseFileLibrary/Components/RoomComponents/RoomMeasurements.cs
    /// </summary>
    public class RoomMeasurements
    {
        public Guid Id { get; set; }
        
        public Guid RoomId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public bool IsRectangular { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Height { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Width { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Depth { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Perimeter { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Area { get; set; }

        // Navigation property back to parent
        [ForeignKey("RoomId")]
        public Room Room { get; set; } = null!;

        public RoomMeasurements()
        {
            SetDefaults();
        }

        public RoomMeasurements(RoomMeasurements toCopy)
        {
            Id = toCopy.Id;
            RoomId = toCopy.RoomId;
            IsRectangular = toCopy.IsRectangular;
            Height = toCopy.Height;
            Width = toCopy.Width;
            Depth = toCopy.Depth;
            Perimeter = toCopy.Perimeter;
            Area = toCopy.Area;
        }

        public void SetDefaults()
        {
            IsRectangular = true;
            Height = 0.0m;
            Width = 0.0m;
            Depth = 0.0m;
            Perimeter = 0.0m;
            Area = 0.0m;
        }
    }
}
