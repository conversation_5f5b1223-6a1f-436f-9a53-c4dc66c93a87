using AutoMapper;
using EnvelopeService.API.Models;
using EnvelopeService.Core.Models;

namespace EnvelopeService.API.Mappings
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // ResourceList and WallDirections mappings (no changes)
            CreateMap<ResourceList, ResourceListDto>().ReverseMap();
            CreateMap<WallDirections, WallDirectionsDto>().ReverseMap();

            // ✅ FIXED: RsiSection mappings - removed problematic ID condition
            CreateMap<RsiSection, RsiSectionDto>().ReverseMap();

            // ✅ FIXED: CodeDescriptionAndComposite mappings - removed problematic ID condition
            CreateMap<CodeDescriptionAndComposite, CodeDescriptionAndCompositeDto>().ReverseMap();

            // ✅ FIXED: Wall mappings - removed problematic ID conditions
            CreateMap<Wall, WallDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Construction != null)
                    {
                        dest.Construction.WallId = dest.Id;
                    }

                    if (dest.Measurements != null)
                    {
                        dest.Measurements.WallId = dest.Id;
                    }
                });

            CreateMap<WallConstruction, WallConstructionDto>().ReverseMap()
                .ForMember(dest => dest.WallId, opt => opt.MapFrom(src => src.WallId))
                .AfterMap((src, dest) =>
                {
                    if (dest.Type == null)
                    {
                        dest.Type = new CodeReference();
                    }
                    if (dest.LintelType == null)
                    {
                        dest.LintelType = new CodeReference();
                    }
                });

            CreateMap<WallMeasurements, WallMeasurementsDto>().ReverseMap()
                .ForMember(dest => dest.Id, opt => opt.Condition((src, dest, srcMember) => srcMember != Guid.Empty))
                .ForMember(dest => dest.WallId, opt => opt.MapFrom(src => src.WallId));

            // CodeReference mappings (no changes)
            CreateMap<CodeReference, CodeReferenceDto>().ReverseMap()
                .ForMember(dest => dest.Id, opt => opt.Condition((src, dest, srcMember) => srcMember != Guid.Empty));

            // UserDefinedLayer mappings (no changes)
            CreateMap<UserDefinedLayer, UserDefinedLayerDto>().ReverseMap()
                .ForMember(dest => dest.Id, opt => opt.Condition((src, dest, srcMember) => srcMember != Guid.Empty));

            // ✅ UPDATED: Ceiling mappings to match new model structure
            CreateMap<Ceiling, CeilingDto>().ReverseMap()
                .ForMember(dest => dest.Id, opt => opt.Condition((src, dest, srcMember) => srcMember != Guid.Empty))
                // ✅ AutoMapper will automatically map Label property
                .AfterMap((src, dest) =>
                {
                    // Ensure relationships are properly set up
                    if (dest.Construction != null)
                    {
                        dest.Construction.CeilingId = dest.Id;
                        
                        // ✅ Ensure CeilingTypeReference has proper ID relationship
                        if (dest.Construction.CeilingTypeReference != null)
                        {
                            dest.Construction.CeilingTypeReferenceId = dest.Construction.CeilingTypeReference.Id;
                        }
                    }

                    if (dest.Measurements != null)
                    {
                        dest.Measurements.CeilingId = dest.Id;
                    }
                });

            // ✅ UPDATED: CeilingConstruction mappings to match new model
            CreateMap<CeilingConstruction, CeilingConstructionDto>().ReverseMap()
                .ForMember(dest => dest.Id, opt => opt.Condition((src, dest, srcMember) => srcMember != Guid.Empty))
                .ForMember(dest => dest.CeilingId, opt => opt.MapFrom(src => src.CeilingId))
                // ✅ Map the CodeReference relationship properly
                .AfterMap((src, dest) =>
                {
                    // Ensure CeilingTypeReference is properly set
                    if (dest.CeilingTypeReference == null)
                    {
                        dest.CeilingTypeReference = new CodeReference();
                    }

                    // Ensure the foreign key relationship is set
                    if (dest.CeilingTypeReference.Id != Guid.Empty)
                    {
                        dest.CeilingTypeReferenceId = dest.CeilingTypeReference.Id;
                    }
                });

            // ✅ UPDATED: CeilingMeasurements mappings to match new model
            CreateMap<CeilingMeasurements, CeilingMeasurementsDto>().ReverseMap()
                .ForMember(dest => dest.Id, opt => opt.Condition((src, dest, srcMember) => srcMember != Guid.Empty))
                .ForMember(dest => dest.CeilingId, opt => opt.MapFrom(src => src.CeilingId));

            // ✅ UPDATED: CeilingType mappings (resource list pattern)
            CreateMap<CeilingType, CeilingTypeDto>().ReverseMap();

            // ✅ UPDATED: CeilingSlope mappings (resource list pattern)
            CreateMap<CeilingSlope, CeilingSlopeDto>().ReverseMap();

            // ✅ FIXED: Floor mappings - removed problematic ID conditions
            CreateMap<Floor, FloorDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Construction != null)
                    {
                        dest.Construction.FloorId = dest.Id;
                    }

                    if (dest.Measurements != null)
                    {
                        dest.Measurements.FloorId = dest.Id;
                    }
                });

            CreateMap<FloorConstruction, FloorConstructionDto>().ReverseMap()
                .ForMember(dest => dest.FloorId, opt => opt.MapFrom(src => src.FloorId));

            CreateMap<FloorMeasurements, FloorMeasurementsDto>().ReverseMap()
                .ForMember(dest => dest.FloorId, opt => opt.MapFrom(src => src.FloorId));

            // ResourceValueList mappings
            CreateMap<ResourceValueList, ResourceValueListDto>().ReverseMap();

            // Door mappings
            CreateMap<DoorTypes, DoorTypesDto>().ReverseMap();

            CreateMap<Door, DoorDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Construction != null)
                    {
                        dest.Construction.DoorId = dest.Id;
                    }

                    if (dest.Measurements != null)
                    {
                        dest.Measurements.DoorId = dest.Id;
                    }
                });

            CreateMap<DoorConstruction, DoorConstructionDto>().ReverseMap()
                .ForMember(dest => dest.DoorId, opt => opt.MapFrom(src => src.DoorId))
                .AfterMap((src, dest) =>
                {
                    if (dest.Type == null)
                    {
                        dest.Type = new DoorTypes();
                    }
                });

            CreateMap<DoorMeasurements, DoorMeasurementsDto>().ReverseMap()
                .ForMember(dest => dest.DoorId, opt => opt.MapFrom(src => src.DoorId));

            // Window mappings
            CreateMap<WindowDirections, WindowDirectionsDto>().ReverseMap();
            CreateMap<WindowTilts, WindowTiltsDto>().ReverseMap();

            CreateMap<Window, WindowDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Construction != null)
                    {
                        dest.Construction.WindowId = dest.Id;
                    }

                    if (dest.Measurements != null)
                    {
                        dest.Measurements.WindowId = dest.Id;
                    }

                    if (dest.Shading != null)
                    {
                        dest.Shading.WindowId = dest.Id;
                    }

                    if (dest.EnergyStar != null)
                    {
                        dest.EnergyStar.WindowId = dest.Id;
                    }
                });

            CreateMap<WindowConstruction, WindowConstructionDto>().ReverseMap()
                .ForMember(dest => dest.WindowId, opt => opt.MapFrom(src => src.WindowId))
                .AfterMap((src, dest) =>
                {
                    if (dest.Type == null)
                    {
                        dest.Type = new CodeReference();
                    }
                });

            CreateMap<WindowMeasurements, WindowMeasurementsDto>().ReverseMap()
                .ForMember(dest => dest.WindowId, opt => opt.MapFrom(src => src.WindowId))
                .AfterMap((src, dest) =>
                {
                    if (dest.Tilt == null)
                    {
                        dest.Tilt = new WindowTilts();
                    }
                });

            CreateMap<WindowShading, WindowShadingDto>().ReverseMap()
                .ForMember(dest => dest.WindowId, opt => opt.MapFrom(src => src.WindowId));

            CreateMap<EnergyStar, EnergyStarDto>().ReverseMap()
                .ForMember(dest => dest.WindowId, opt => opt.MapFrom(src => src.WindowId));

            // FloorHeader mappings - with automatic text population from static resource definitions
            CreateMap<FloorHeaderDirections, FloorHeaderDirectionsDto>()
                .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.English, opt => opt.MapFrom(src => src.English))
                .ForMember(dest => dest.French, opt => opt.MapFrom(src => src.French))
                .ForMember(dest => dest.IsUserSpecified, opt => opt.MapFrom(src => src.IsUserSpecified));

            CreateMap<FloorHeaderDirectionsDto, FloorHeaderDirections>()
                .ConstructUsing(src =>
                    FloorHeaderDirections.All.FirstOrDefault(fhd => fhd.Code == (src.Code ?? "1")) ??
                    FloorHeaderDirections.NotApplicable);

            CreateMap<FloorHeader, FloorHeaderDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Construction != null)
                    {
                        dest.Construction.FloorHeaderId = dest.Id;
                    }

                    if (dest.Measurements != null)
                    {
                        dest.Measurements.FloorHeaderId = dest.Id;
                    }
                });

            CreateMap<FloorHeaderConstruction, FloorHeaderConstructionDto>().ReverseMap()
                .ForMember(dest => dest.FloorHeaderId, opt => opt.MapFrom(src => src.FloorHeaderId))
                .AfterMap((src, dest) =>
                {
                    if (dest.Type == null)
                    {
                        dest.Type = new CodeReference();
                    }
                });

            CreateMap<FloorHeaderMeasurements, FloorHeaderMeasurementsDto>().ReverseMap()
                .ForMember(dest => dest.FloorHeaderId, opt => opt.MapFrom(src => src.FloorHeaderId));

            // Room mappings - with automatic text population from static resource definitions
            CreateMap<RoomTypes, RoomTypesDto>()
                .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.English, opt => opt.MapFrom(src => src.English))
                .ForMember(dest => dest.French, opt => opt.MapFrom(src => src.French))
                .ForMember(dest => dest.IsUserSpecified, opt => opt.MapFrom(src => src.IsUserSpecified));

            CreateMap<RoomTypesDto, RoomTypes>()
                .ConstructUsing(src =>
                    RoomTypes.All.FirstOrDefault(rt => rt.Code == (src.Code ?? "1")) ??
                    RoomTypes.Kitchen);

            CreateMap<RoomFloors, RoomFloorsDto>()
                .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.English, opt => opt.MapFrom(src => src.English))
                .ForMember(dest => dest.French, opt => opt.MapFrom(src => src.French))
                .ForMember(dest => dest.IsUserSpecified, opt => opt.MapFrom(src => src.IsUserSpecified));

            CreateMap<RoomFloorsDto, RoomFloors>()
                .ConstructUsing(src =>
                    RoomFloors.All.FirstOrDefault(rf => rf.Code == (src.Code ?? "1")) ??
                    RoomFloors.GroundFloor);

            CreateMap<Room, RoomDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Construction != null)
                    {
                        dest.Construction.RoomId = dest.Id;
                    }

                    if (dest.Measurements != null)
                    {
                        dest.Measurements.RoomId = dest.Id;
                    }
                });

            CreateMap<RoomConstruction, RoomConstructionDto>().ReverseMap()
                .ForMember(dest => dest.RoomId, opt => opt.MapFrom(src => src.RoomId))
                .AfterMap((src, dest) =>
                {
                    if (dest.Type == null)
                    {
                        dest.Type = RoomTypes.Kitchen;
                    }
                    if (dest.Floor == null)
                    {
                        dest.Floor = RoomFloors.GroundFloor;
                    }
                    if (dest.FoundationBelow == null)
                    {
                        dest.FoundationBelow = new CodeAndText();
                    }
                });

            CreateMap<RoomMeasurements, RoomMeasurementsDto>().ReverseMap()
                .ForMember(dest => dest.RoomId, opt => opt.MapFrom(src => src.RoomId));
        }
    }
}
