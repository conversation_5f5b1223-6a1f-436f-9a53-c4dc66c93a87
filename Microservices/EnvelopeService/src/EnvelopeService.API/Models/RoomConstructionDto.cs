using System;

namespace EnvelopeService.API.Models
{
    /// <summary>
    /// Room construction DTO
    /// </summary>
    public class RoomConstructionDto
    {
        public Guid Id { get; set; }
        
        public Guid RoomId { get; set; }

        // Room type
        public RoomTypesDto Type { get; set; } = new RoomTypesDto();

        // Floor level
        public RoomFloorsDto Floor { get; set; } = new RoomFloorsDto();

        // Foundation below
        public CodeAndTextDto FoundationBelow { get; set; } = new CodeAndTextDto();
    }
}
