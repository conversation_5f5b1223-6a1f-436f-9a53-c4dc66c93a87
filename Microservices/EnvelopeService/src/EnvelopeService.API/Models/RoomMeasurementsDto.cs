using System;

namespace EnvelopeService.API.Models
{
    /// <summary>
    /// Room measurements DTO
    /// </summary>
    public class RoomMeasurementsDto
    {
        public Guid Id { get; set; }
        
        public Guid RoomId { get; set; }

        public bool IsRectangular { get; set; }

        public decimal Height { get; set; }

        public decimal Width { get; set; }

        public decimal Depth { get; set; }

        public decimal Perimeter { get; set; }

        public decimal Area { get; set; }
    }
}
