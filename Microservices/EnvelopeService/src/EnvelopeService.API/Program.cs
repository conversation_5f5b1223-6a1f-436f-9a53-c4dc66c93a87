using System;
using System.Data;
using System.Linq;
using System.Text.Json.Serialization;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using EnvelopeService.API.Models;
using EnvelopeService.Core.Interfaces;
using EnvelopeService.Core.Services;
using EnvelopeService.Infrastructure.Data;
using EnvelopeService.Infrastructure.Repositories;
using Hot2K.Shared.Validation.Extensions;
using Hot2K.Shared.Validation.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
    options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
});

// Configure Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Envelope Service API",
        Version = "v1",
        Description = "A microservice for managing building envelope in the HOT2000 system",
        Contact = new OpenApiContact
        {
            Name = "HOT2000 Team"
        }
    });
});

// Configure DbContext with schema support
builder.Services.AddDbContext<EnvelopDbContext>(options =>
{
    options.UseSqlServer(
        builder.Configuration.GetConnectionString("DefaultConnection"),
        sqlOptions => 
        {
            sqlOptions.EnableRetryOnFailure(
                maxRetryCount: 5,
                maxRetryDelay: TimeSpan.FromSeconds(30),
                errorNumbersToAdd: null);
            sqlOptions.CommandTimeout(300);
            // Add this line to set the migrations history table schema
            sqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", "envelope");
        });
});

// Configure AutoMapper
builder.Services.AddAutoMapper(typeof(Program).Assembly);

// Configure Hot2K Validation Services - Load both wall and ceiling validation rules
var wallDockerPath = "ValidationRules/wall-validation.json";
var wallDevPath = "../../../ValidationRules/wall-validation.json";
var wallValidationRulesPath = File.Exists(wallDockerPath) ? wallDockerPath : wallDevPath;

var ceilingDockerPath = "ValidationRules/ceiling-validation.json";
var ceilingDevPath = "../../../ValidationRules/ceiling-validation.json";
var ceilingValidationRulesPath = File.Exists(ceilingDockerPath) ? ceilingDockerPath : ceilingDevPath;

// Read both validation rule files
var wallJsonRules = File.ReadAllText(wallValidationRulesPath);
var ceilingJsonRules = File.ReadAllText(ceilingValidationRulesPath);

// Combine the validation rules into a single JSON structure
var combinedRules = CombineValidationRules(wallJsonRules, ceilingJsonRules);

builder.Services.AddHot2KDeclarativeValidationWithJson(
    combinedRules,
    defaultBuildingType: "SingleFamily"
);

// Configure Repositories and Services
builder.Services.AddScoped<IWallService, WallService>();
builder.Services.AddScoped<IWallRepository, WallRepository>();
builder.Services.AddScoped<ICeilingService, CeilingService>();
builder.Services.AddScoped<ICeilingRepository, CeilingRepository>();
builder.Services.AddScoped<IFloorService, FloorService>();
builder.Services.AddScoped<IFloorRepository, FloorRepository>();
builder.Services.AddScoped<IFloorHeaderService, FloorHeaderService>();
builder.Services.AddScoped<IFloorHeaderRepository, FloorHeaderRepository>();
builder.Services.AddScoped<IDoorService, DoorService>();
builder.Services.AddScoped<IDoorRepository, DoorRepository>();
builder.Services.AddScoped<IWindowService, WindowService>();
builder.Services.AddScoped<IWindowRepository, WindowRepository>();
builder.Services.AddScoped<IRoomService, RoomService>();
builder.Services.AddScoped<IRoomRepository, RoomRepository>();
builder.Services.AddScoped<IThermalPerformanceService, ThermalPerformanceService>();

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll",
        builder => builder
            .AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader());
});

var app = builder.Build();

// Enable Swagger in all environments
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Envelope Service API V1");
    c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
});
// Replace your database migration section with this more robust version:

using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    var logger = services.GetRequiredService<ILogger<Program>>();
    
    try
    {
        logger.LogInformation("Starting database initialization...");
        var dbContext = services.GetRequiredService<EnvelopDbContext>();
        
        // Test database connection first
        logger.LogInformation("Testing database connection...");
        var canConnect = await dbContext.Database.CanConnectAsync();
        
        if (!canConnect)
        {
            logger.LogError("Cannot connect to database");
            throw new InvalidOperationException("Database connection failed");
        }
        
        logger.LogInformation("Database connection successful");
        
        var connection = dbContext.Database.GetDbConnection();
        if (connection.State != ConnectionState.Open)
            await connection.OpenAsync();

        // Check for existing tables
        bool hasExistingTables = false;
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = 'envelope'";
            
            var tableCount = Convert.ToInt32(await command.ExecuteScalarAsync());
            hasExistingTables = tableCount > 0;
            
            logger.LogInformation($"Found {tableCount} existing tables in envelope schema");
        }
        
        if (hasExistingTables)
        {
            logger.LogInformation("Dropping existing envelope schema for clean start...");
            
            try
            {
                // More robust schema dropping
                using (var command = connection.CreateCommand())
                {
                    // Drop constraints
                    command.CommandText = @"
                        DECLARE @sql NVARCHAR(MAX) = ''
                        SELECT @sql = @sql + 'ALTER TABLE [envelope].[' + TABLE_NAME + '] DROP CONSTRAINT [' + CONSTRAINT_NAME + '];' + CHAR(13)
                        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
                        WHERE CONSTRAINT_SCHEMA = 'envelope' AND CONSTRAINT_TYPE = 'FOREIGN KEY'
                        IF LEN(@sql) > 0 EXEC sp_executesql @sql";
                    await command.ExecuteNonQueryAsync();
                    
                    // Drop tables
                    command.CommandText = @"
                        DECLARE @sql NVARCHAR(MAX) = ''
                        SELECT @sql = @sql + 'DROP TABLE [envelope].[' + TABLE_NAME + '];' + CHAR(13)
                        FROM INFORMATION_SCHEMA.TABLES 
                        WHERE TABLE_SCHEMA = 'envelope'
                        IF LEN(@sql) > 0 EXEC sp_executesql @sql";
                    await command.ExecuteNonQueryAsync();
                    
                    // Drop schema
                    command.CommandText = @"
                        IF EXISTS (SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'envelope')
                        BEGIN
                            DROP SCHEMA [envelope]
                        END";
                    await command.ExecuteNonQueryAsync();
                }
                
                logger.LogInformation("Envelope schema dropped successfully");
            }
            catch (Exception dropEx)
            {
                logger.LogWarning($"Error during schema drop: {dropEx.Message}");
                logger.LogInformation("Continuing with migration...");
            }
        }
        
        // Create schema
        using (var command = connection.CreateCommand())
        {
            command.CommandText = "IF NOT EXISTS (SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'envelope') BEGIN EXEC('CREATE SCHEMA envelope') END";
            await command.ExecuteNonQueryAsync();
        }
        
        // Apply migrations
        logger.LogInformation("Applying migrations...");
        await dbContext.Database.MigrateAsync();
        
        logger.LogInformation("Database initialization completed successfully");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Database initialization failed");

        // Don't throw in startup - let the service run without DB for debugging
        logger.LogWarning("Service will start without database initialization");
    }
}

app.Logger.LogInformation("About to initialize validation service...");

// Initialize declarative validation service
app.Logger.LogInformation("Starting validation service initialization...");
using (var scope = app.Services.CreateScope())
{
    try
    {
        app.Logger.LogInformation("Getting validation service from DI container...");
        var validationService = scope.ServiceProvider.GetRequiredService<IDeclarativeValidationService>();
        app.Logger.LogInformation("Calling InitializeAsync on validation service...");
        await validationService.InitializeAsync();
        app.Logger.LogInformation("Declarative validation service initialized successfully");
    }
    catch (Exception ex)
    {
        app.Logger.LogError(ex, "Failed to initialize validation service");
    }
}

if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/error");
    app.UseHsts();
}

app.UseCors("AllowAll");
app.UseRouting();
app.UseAuthorization();
app.MapControllers();

app.Run();

// Helper method to combine multiple validation rule JSON files
static string CombineValidationRules(params string[] jsonRules)
{
    using var combinedDocument = System.Text.Json.JsonDocument.Parse("{}");
    var combinedDtos = new Dictionary<string, object>();

    foreach (var jsonRule in jsonRules)
    {
        using var document = System.Text.Json.JsonDocument.Parse(jsonRule);
        if (document.RootElement.TryGetProperty("dtos", out var dtosElement))
        {
            foreach (var dto in dtosElement.EnumerateObject())
            {
                combinedDtos[dto.Name] = System.Text.Json.JsonSerializer.Deserialize<object>(dto.Value.GetRawText());
            }
        }
    }

    var result = new { dtos = combinedDtos };
    return System.Text.Json.JsonSerializer.Serialize(result, new System.Text.Json.JsonSerializerOptions
    {
        WriteIndented = true
    });
}