using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using AutoMapper;
using EnvelopeService.API.Models;
using EnvelopeService.Core.Interfaces;
using EnvelopeService.Core.Models;
using Hot2K.Shared.Validation.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace EnvelopeService.API.Controllers
{
    [ApiController]
    [Route("api/envelope")]
    public class EnvelopeController : ControllerBase
    {
        private readonly IWallService _wallService;
        private readonly ICeilingService _ceilingService;
        private readonly IFloorService _floorService;
        private readonly IFloorHeaderService _floorHeaderService;
        private readonly IDoorService _doorService;
        private readonly IWindowService _windowService;
        private readonly IRoomService _roomService;
        private readonly IThermalPerformanceService _thermalPerformanceService;
        private readonly IDeclarativeValidationService _validationService;
        private readonly IMapper _mapper;
        private readonly ILogger<EnvelopeController> _logger;

        public EnvelopeController(
            IWallService wallService,
            ICeilingService ceilingService,
            IFloorService floorService,
            IFloorHeaderService floorHeaderService,
            IDoorService doorService,
            IWindowService windowService,
            IRoomService roomService,
            IThermalPerformanceService thermalPerformanceService,
            IDeclarativeValidationService validationService,
            IMapper mapper,
            ILogger<EnvelopeController> logger)
        {
            _wallService = wallService ?? throw new ArgumentNullException(nameof(wallService));
            _ceilingService = ceilingService ?? throw new ArgumentNullException(nameof(ceilingService));
            _floorService = floorService ?? throw new ArgumentNullException(nameof(floorService));
            _floorHeaderService = floorHeaderService ?? throw new ArgumentNullException(nameof(floorHeaderService));
            _doorService = doorService ?? throw new ArgumentNullException(nameof(doorService));
            _windowService = windowService ?? throw new ArgumentNullException(nameof(windowService));
            _roomService = roomService ?? throw new ArgumentNullException(nameof(roomService));
            _thermalPerformanceService = thermalPerformanceService ?? throw new ArgumentNullException(nameof(thermalPerformanceService));
            _validationService = validationService ?? throw new ArgumentNullException(nameof(validationService));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        // GET: api/envelope/{houseId}/walls
        [HttpGet("{houseId}/walls")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<List<WallDto>>> GetWalls(Guid houseId)
        {
            _logger.LogInformation("Getting all walls for house with ID: {HouseId}", houseId);

            var walls = await _wallService.GetWallsByHouseIdAsync(houseId);

            if (walls == null || !walls.Any())
            {
                _logger.LogWarning("No walls found for house with ID: {HouseId}", houseId);
                return NotFound();
            }

            var wallDtos = _mapper.Map<List<WallDto>>(walls);
            return Ok(wallDtos);
        }
        
        // GET: api/envelope/walls/{id}
        [HttpGet("walls/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<WallDto>> GetWallById(Guid id)
        {
            _logger.LogInformation("Getting wall with ID: {WallId}", id);
            
            var wall = await _wallService.GetWallByIdAsync(id);
            
            if (wall == null)
            {
                _logger.LogWarning("Wall with ID: {WallId} not found", id);
                return NotFound();
            }
            
            var wallDto = _mapper.Map<WallDto>(wall);
            return Ok(wallDto);
        }

        // POST: api/envelope/walls
        /// <summary>
        /// Creates a new wall with Hot2K declarative validation framework
        /// Uses external JSON/YAML rules instead of hardcoded validation
        /// </summary>
        [HttpPost("walls")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<WallDto>> AddWall([FromBody] JsonElement jsonElement, [FromQuery] string? buildingType = null)
        {
            if (jsonElement.ValueKind == JsonValueKind.Undefined || jsonElement.ValueKind == JsonValueKind.Null)
            {
                return BadRequest(new { error = "WallDto is required" });
            }

            // Use declarative validation from JSON/YAML rules
            var buildingTypeToUse = buildingType ?? "SingleFamily"; // Default building type

            // Validate the raw JSON against the camelCase validation rules
            var validationResult = await _validationService.ValidateJsonAsync(jsonElement.GetRawText(), "WallDto", buildingTypeToUse);

            // Debug: Log validation result details
            _logger.LogInformation("DEBUG: POST Wall Validation IsValid: {IsValid}, Error Count: {ErrorCount}",
                validationResult.IsValid, validationResult.Errors?.Count() ?? 0);

            if (validationResult.Errors?.Any() == true)
            {
                foreach (var error in validationResult.Errors)
                {
                    _logger.LogInformation("DEBUG: POST Wall Validation Error - Field: {Field}, Message: {Message}, Value: {Value}",
                        error.PropertyName, error.ErrorMessage, error.AttemptedValue);
                }
            }

            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Declarative validation failed for Wall creation. Building Type: {BuildingType}, Errors: {Errors}",
                    buildingTypeToUse, string.Join(", ", validationResult.Errors.Select(e => $"{e.PropertyName}: {e.ErrorMessage}")));

                return BadRequest(new
                {
                    error = "Validation failed",
                    buildingType = buildingTypeToUse,
                    validationErrors = validationResult.Errors.Select(e => new
                    {
                        field = e.PropertyName,
                        message = e.ErrorMessage,
                        attemptedValue = e.AttemptedValue
                    })
                });
            }

            _logger.LogInformation("Declarative validation passed for Wall creation. Building Type: {BuildingType}", buildingTypeToUse);

            // Deserialize the JSON to DTO after validation passes
            var wallDto = JsonSerializer.Deserialize<WallDto>(jsonElement.GetRawText(), new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (wallDto == null)
            {
                return BadRequest(new { error = "Failed to deserialize WallDto" });
            }

            try
            {
                _logger.LogInformation("Adding new wall for house with ID: {HouseId}", wallDto.HouseId);

                // UPDATED: Auto-populate EnglishText/FrenchText if only code provided, but respect IsUserSpecified
                if (!string.IsNullOrEmpty(wallDto.FacingDirection?.Code) &&
                    string.IsNullOrEmpty(wallDto.FacingDirection?.EnglishText))
                {
                    // Get the direction by code, preserving the IsUserSpecified value from DTO
                    var direction = WallDirections.FromCode(
                        wallDto.FacingDirection.Code,
                        wallDto.FacingDirection.IsUserSpecified);

                    wallDto.FacingDirection.EnglishText = direction.EnglishText;
                    wallDto.FacingDirection.FrenchText = direction.FrenchText;
                    // Keep the IsUserSpecified value as provided by the user
                }

                var wall = _mapper.Map<Wall>(wallDto);

                if (wall.Id == Guid.Empty)
                {
                    wall.Id = Guid.NewGuid();
                }

                var createdWall = await _wallService.AddWallAsync(wall);
                var createdWallDto = _mapper.Map<WallDto>(createdWall);

                return CreatedAtAction(nameof(GetWallById), new { id = createdWallDto.Id }, createdWallDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding wall for house with ID: {HouseId}", wallDto.HouseId);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while adding the wall");
            }
        }

        // PUT: api/envelope/walls/{id}
        /// <summary>
        /// Updates an existing wall with Hot2K declarative validation framework
        /// Uses external JSON/YAML rules instead of hardcoded validation
        /// </summary>
        [HttpPut("walls/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateWall(Guid id, [FromBody] JsonElement jsonElement, [FromQuery] string? buildingType = null)
        {
            if (jsonElement.ValueKind == JsonValueKind.Undefined || jsonElement.ValueKind == JsonValueKind.Null)
            {
                return BadRequest(new { error = "WallDto is required" });
            }

            // Use declarative validation from JSON/YAML rules
            var buildingTypeToUse = buildingType ?? "SingleFamily"; // Default building type

            // Validate the raw JSON against the camelCase validation rules
            var validationResult = await _validationService.ValidateJsonAsync(jsonElement.GetRawText(), "WallDto", buildingTypeToUse);

            // Debug: Log validation result details
            _logger.LogInformation("DEBUG: PUT Wall Validation IsValid: {IsValid}, Error Count: {ErrorCount}",
                validationResult.IsValid, validationResult.Errors?.Count() ?? 0);

            if (validationResult.Errors?.Any() == true)
            {
                foreach (var error in validationResult.Errors)
                {
                    _logger.LogInformation("DEBUG: PUT Wall Validation Error - Field: {Field}, Message: {Message}, Value: {Value}",
                        error.PropertyName, error.ErrorMessage, error.AttemptedValue);
                }
            }

            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Declarative validation failed for Wall update. Building Type: {BuildingType}, Errors: {Errors}",
                    buildingTypeToUse, string.Join(", ", validationResult.Errors.Select(e => $"{e.PropertyName}: {e.ErrorMessage}")));

                return BadRequest(new
                {
                    error = "Validation failed",
                    buildingType = buildingTypeToUse,
                    validationErrors = validationResult.Errors.Select(e => new
                    {
                        field = e.PropertyName,
                        message = e.ErrorMessage,
                        attemptedValue = e.AttemptedValue
                    })
                });
            }

            _logger.LogInformation("Declarative validation passed for Wall update. Building Type: {BuildingType}", buildingTypeToUse);

            // Deserialize the JSON to DTO after validation passes
            var wallDto = JsonSerializer.Deserialize<WallDto>(jsonElement.GetRawText(), new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (wallDto == null)
            {
                return BadRequest(new { error = "Failed to deserialize WallDto" });
            }

            if (id != wallDto.Id)
            {
                _logger.LogWarning("Wall ID mismatch: {PathId} vs {BodyId}", id, wallDto.Id);
                return BadRequest("The ID in the path must match the ID in the body");
            }

            try
            {
                _logger.LogInformation("Updating wall with ID: {WallId}", id);

                // UPDATED: Same logic - respect manual IsUserSpecified
                if (!string.IsNullOrEmpty(wallDto.FacingDirection?.Code) &&
                    string.IsNullOrEmpty(wallDto.FacingDirection?.EnglishText))
                {
                    var direction = WallDirections.FromCode(
                        wallDto.FacingDirection.Code,
                        wallDto.FacingDirection.IsUserSpecified);

                    wallDto.FacingDirection.EnglishText = direction.EnglishText;
                    wallDto.FacingDirection.FrenchText = direction.FrenchText;
                }

                var existingWall = await _wallService.GetWallByIdAsync(id);
                if (existingWall == null)
                {
                    _logger.LogWarning("Wall with ID: {WallId} not found for update", id);
                    return NotFound();
                }

                var wall = _mapper.Map<Wall>(wallDto);
                wall.Id = id;

                await _wallService.UpdateWallAsync(wall);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating wall with ID: {WallId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while updating the wall");
            }
        }

        // DELETE: api/envelope/walls/{id}
        [HttpDelete("walls/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteWall(Guid id)
        {
            try
            {
                _logger.LogInformation("Deleting wall with ID: {WallId}", id);
                
                var existingWall = await _wallService.GetWallByIdAsync(id);
                if (existingWall == null)
                {
                    _logger.LogWarning("Wall with ID: {WallId} not found for deletion", id);
                    return NotFound();
                }
                
                await _wallService.DeleteWallAsync(id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting wall with ID: {WallId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while deleting the wall");
            }
        }

        // NEW: Get available wall directions
        [HttpGet("walls/directions")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<List<WallDirectionsDto>> GetWallDirections()
        {
            _logger.LogInformation("Getting all available wall directions");
            
            var directions = WallDirections.GetAll();
            var directionDtos = _mapper.Map<List<WallDirectionsDto>>(directions);
            
            return Ok(directionDtos);
        }

        // GET: api/envelope/{houseId}/ceilings
        [HttpGet("{houseId}/ceilings")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<List<CeilingDto>>> GetCeilings(Guid houseId)
        {
            _logger.LogInformation("Getting all ceilings for house with ID: {HouseId}", houseId);
            
            var ceilings = await _ceilingService.GetCeilingsByHouseIdAsync(houseId);
            
            if (ceilings == null || !ceilings.Any())
            {
                _logger.LogWarning("No ceilings found for house with ID: {HouseId}", houseId);
                return NotFound();
            }
            
            var ceilingDtos = _mapper.Map<List<CeilingDto>>(ceilings);
            
            return Ok(ceilingDtos);
        }

        // GET: api/envelope/ceilings/{id}
        [HttpGet("ceilings/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<CeilingDto>> GetCeilingById(Guid id)
        {
            _logger.LogInformation("Getting ceiling with ID: {CeilingId}", id);
    
            var ceiling = await _ceilingService.GetCeilingByIdAsync(id);
    
            if (ceiling == null)
            {
                _logger.LogWarning("Ceiling with ID: {CeilingId} not found", id);
                return NotFound();
            }
    
            var ceilingDto = _mapper.Map<CeilingDto>(ceiling);
    
            return Ok(ceilingDto);
        }
        
        // POST: api/envelope/ceilings
        /// <summary>
        /// Creates a new ceiling with Hot2K declarative validation framework
        /// Uses external JSON rules based on CeilingPage.cpp validation
        /// </summary>
        [HttpPost("ceilings")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<CeilingDto>> AddCeiling([FromBody] JsonElement jsonElement, [FromQuery] string? buildingType = null)
        {
            if (jsonElement.ValueKind == JsonValueKind.Undefined || jsonElement.ValueKind == JsonValueKind.Null)
            {
                return BadRequest(new { error = "CeilingDto is required" });
            }

            // Use declarative validation from JSON rules based on CeilingPage.cpp
            var buildingTypeToUse = buildingType ?? "SingleFamily"; // Default building type

            // Validate the raw JSON against the camelCase validation rules
            var validationResult = await _validationService.ValidateJsonAsync(jsonElement.GetRawText(), "CeilingDto", buildingTypeToUse);

            // Debug: Log validation result details
            _logger.LogInformation("DEBUG: POST Ceiling Validation IsValid: {IsValid}, Error Count: {ErrorCount}",
                validationResult.IsValid, validationResult.Errors?.Count() ?? 0);

            if (validationResult.Errors?.Any() == true)
            {
                foreach (var error in validationResult.Errors)
                {
                    _logger.LogInformation("DEBUG: POST Ceiling Validation Error - Field: {Field}, Message: {Message}, Value: {Value}",
                        error.PropertyName, error.ErrorMessage, error.AttemptedValue);
                }
            }

            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Declarative validation failed for Ceiling creation. Building Type: {BuildingType}, Errors: {Errors}",
                    buildingTypeToUse, string.Join(", ", validationResult.Errors.Select(e => $"{e.PropertyName}: {e.ErrorMessage}")));

                return BadRequest(new
                {
                    error = "Validation failed",
                    buildingType = buildingTypeToUse,
                    validationErrors = validationResult.Errors.Select(e => new
                    {
                        field = e.PropertyName,
                        message = e.ErrorMessage,
                        attemptedValue = e.AttemptedValue
                    })
                });
            }

            _logger.LogInformation("Declarative validation passed for Ceiling creation. Building Type: {BuildingType}", buildingTypeToUse);

            // Deserialize the JSON to DTO after validation passes
            var ceilingDto = JsonSerializer.Deserialize<CeilingDto>(jsonElement.GetRawText(), new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (ceilingDto == null)
            {
                return BadRequest(new { error = "Failed to deserialize CeilingDto" });
            }
            
            try
            {
                _logger.LogInformation("Adding new ceiling for house with ID: {HouseId}", ceilingDto.HouseId);
                
                var ceiling = _mapper.Map<Ceiling>(ceilingDto);
                if (ceiling.Id == Guid.Empty)
                {
                    ceiling.Id = Guid.NewGuid();
                }
        
                // Ensure proper initialization of nested objects
                if (ceiling.Construction == null)
                    ceiling.Construction = new CeilingConstruction();
            
                if (ceiling.Measurements == null)
                    ceiling.Measurements = new CeilingMeasurements();
            
                // Handle CeilingType - Auto-populate text fields if only code provided
                if (!string.IsNullOrEmpty(ceilingDto.Construction?.Type?.Code))
                {
                    var typeCode = ceilingDto.Construction.Type.Code;
                    var isUserSpecified = ceilingDto.Construction.Type.IsUserSpecified;

                    // Find the type by code and preserve IsUserSpecified value
                    var ceilingType = CeilingType.All.FirstOrDefault(t => t.Code == typeCode) ?? CeilingType.AtticGable;

                    ceiling.Construction.Type = new CeilingType
                    {
                        Code = typeCode,
                        EnglishText = ceilingType.EnglishText,
                        FrenchText = ceilingType.FrenchText,
                        IsUserSpecified = isUserSpecified
                    };

                    // Update DTO to return populated values
                    ceilingDto.Construction.Type.EnglishText = ceilingType.EnglishText;
                    ceilingDto.Construction.Type.FrenchText = ceilingType.FrenchText;
                }
        
                // Handle CeilingSlope - Auto-populate text and value fields if only code provided
                if (!string.IsNullOrEmpty(ceilingDto.Measurements?.Slope?.Code))
                {
                    var slopeCode = ceilingDto.Measurements.Slope.Code;
                    var userValue = ceilingDto.Measurements.Slope.Value; // Preserve user's value

                    _logger.LogInformation("DEBUG: POST Slope Processing - Code: {Code}, UserValue: {UserValue}, IsUserSpecified: {IsUserSpecified}",
                        slopeCode, userValue, ceilingDto.Measurements.Slope.IsUserSpecified);

                    // Find the slope by code
                    var ceilingSlope = CeilingSlope.All.FirstOrDefault(s => s.Code == slopeCode) ?? CeilingSlope.Slope4Twelfth;

                    // For code "0" (user-specified), always use user's value and set isUserSpecified = true
                    // For other codes, use resource value and isUserSpecified from DTO or resource
                    bool isUserSpecified;
                    decimal finalValue;

                    if (slopeCode == "0")
                    {
                        // Code "0" means user-specified - always use user's value
                        isUserSpecified = true;
                        finalValue = userValue; // Use the value provided by user
                        _logger.LogInformation("DEBUG: POST Slope Code=0 (User-Specified) - Using UserValue: {FinalValue}", finalValue);
                    }
                    else
                    {
                        // All other codes - always use resource default values (ignore user input)
                        isUserSpecified = false;
                        finalValue = ceilingSlope.Value; // Always use resource default value
                        _logger.LogInformation("DEBUG: POST Slope Code={Code} - Using Resource Default Value: {FinalValue}",
                            slopeCode, finalValue);
                    }

                    ceiling.Measurements.Slope = new CeilingSlope
                    {
                        Code = slopeCode,
                        EnglishText = ceilingSlope.EnglishText,
                        FrenchText = ceilingSlope.FrenchText,
                        Value = finalValue,
                        IsUserSpecified = isUserSpecified
                    };

                    // Update DTO to return populated values
                    ceilingDto.Measurements.Slope.EnglishText = ceilingSlope.EnglishText;
                    ceilingDto.Measurements.Slope.FrenchText = ceilingSlope.FrenchText;
                    ceilingDto.Measurements.Slope.Value = finalValue;
                    ceilingDto.Measurements.Slope.IsUserSpecified = isUserSpecified;

                    _logger.LogInformation("DEBUG: POST Slope Final Result - Code: {Code}, Value: {Value}, IsUserSpecified: {IsUserSpecified}",
                        slopeCode, finalValue, isUserSpecified);
                }
        
                var createdCeiling = await _ceilingService.AddCeilingAsync(ceiling);
                var createdCeilingDto = _mapper.Map<CeilingDto>(createdCeiling);
        
                return CreatedAtAction(nameof(GetCeilings), new { houseId = ceilingDto.HouseId }, createdCeilingDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding ceiling for house with ID: {HouseId}", ceilingDto.HouseId);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while adding the ceiling");
            }
        }

        // PUT: api/envelope/ceilings/{id}
        /// <summary>
        /// Updates an existing ceiling with Hot2K declarative validation framework
        /// Uses external JSON rules based on CeilingPage.cpp validation
        /// </summary>
        [HttpPut("ceilings/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<CeilingDto>> UpdateCeiling(Guid id, [FromBody] JsonElement jsonElement, [FromQuery] string? buildingType = null)
        {
            if (jsonElement.ValueKind == JsonValueKind.Undefined || jsonElement.ValueKind == JsonValueKind.Null)
            {
                return BadRequest(new { error = "CeilingDto is required" });
            }

            // Use declarative validation from JSON rules based on CeilingPage.cpp
            var buildingTypeToUse = buildingType ?? "SingleFamily"; // Default building type

            // Validate the raw JSON against the camelCase validation rules
            var validationResult = await _validationService.ValidateJsonAsync(jsonElement.GetRawText(), "CeilingDto", buildingTypeToUse);

            // Debug: Log validation result details
            _logger.LogInformation("DEBUG: PUT Ceiling Validation IsValid: {IsValid}, Error Count: {ErrorCount}",
                validationResult.IsValid, validationResult.Errors?.Count() ?? 0);

            if (validationResult.Errors?.Any() == true)
            {
                foreach (var error in validationResult.Errors)
                {
                    _logger.LogInformation("DEBUG: PUT Ceiling Validation Error - Field: {Field}, Message: {Message}, Value: {Value}",
                        error.PropertyName, error.ErrorMessage, error.AttemptedValue);
                }
            }

            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Declarative validation failed for Ceiling update. Building Type: {BuildingType}, Errors: {Errors}",
                    buildingTypeToUse, string.Join(", ", validationResult.Errors.Select(e => $"{e.PropertyName}: {e.ErrorMessage}")));

                return BadRequest(new
                {
                    error = "Validation failed",
                    buildingType = buildingTypeToUse,
                    validationErrors = validationResult.Errors.Select(e => new
                    {
                        field = e.PropertyName,
                        message = e.ErrorMessage,
                        attemptedValue = e.AttemptedValue
                    })
                });
            }

            _logger.LogInformation("Declarative validation passed for Ceiling update. Building Type: {BuildingType}", buildingTypeToUse);

            // Deserialize the JSON to DTO after validation passes
            var ceilingDto = JsonSerializer.Deserialize<CeilingDto>(jsonElement.GetRawText(), new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (ceilingDto == null)
            {
                return BadRequest(new { error = "Failed to deserialize CeilingDto" });
            }

            if (id != ceilingDto.Id)
            {
                _logger.LogWarning("Ceiling ID mismatch: {PathId} vs {BodyId}", id, ceilingDto.Id);
                return BadRequest("The ID in the path must match the ID in the body");
            }
    
            try
            {
                _logger.LogInformation("Updating ceiling with ID: {CeilingId}", id);
        
                // Explicitly set the ID to ensure it's correctly mapped
                ceilingDto.Id = id;
        
                var ceiling = _mapper.Map<Ceiling>(ceilingDto);
        
                // Handle CeilingType - Auto-populate text fields if only code provided
                if (!string.IsNullOrEmpty(ceilingDto.Construction?.Type?.Code))
                {
                    var typeCode = ceilingDto.Construction.Type.Code;
                    var isUserSpecified = ceilingDto.Construction.Type.IsUserSpecified;
                    
                    // Find the type by code and preserve IsUserSpecified value
                    var ceilingType = CeilingType.All.FirstOrDefault(t => t.Code == typeCode) ?? CeilingType.AtticGable;
                    
                    ceiling.Construction.Type = new CeilingType
                    {
                        Code = typeCode,
                        EnglishText = ceilingType.EnglishText,
                        FrenchText = ceilingType.FrenchText,
                        IsUserSpecified = isUserSpecified
                    };
                    
                    // Update DTO to return populated values
                    ceilingDto.Construction.Type.EnglishText = ceilingType.EnglishText;
                    ceilingDto.Construction.Type.FrenchText = ceilingType.FrenchText;
                }
        
                // Handle CeilingSlope - Auto-populate text and value fields if only code provided
                if (!string.IsNullOrEmpty(ceilingDto.Measurements?.Slope?.Code))
                {
                    var slopeCode = ceilingDto.Measurements.Slope.Code;
                    var userValue = ceilingDto.Measurements.Slope.Value; // Preserve user's value

                    _logger.LogInformation("DEBUG: PUT Slope Processing - Code: {Code}, UserValue: {UserValue}, IsUserSpecified: {IsUserSpecified}",
                        slopeCode, userValue, ceilingDto.Measurements.Slope.IsUserSpecified);

                    // Find the slope by code
                    var ceilingSlope = CeilingSlope.All.FirstOrDefault(s => s.Code == slopeCode) ?? CeilingSlope.Slope4Twelfth;

                    // For code "0" (user-specified), always use user's value and set isUserSpecified = true
                    // For other codes, use resource value and isUserSpecified from DTO or resource
                    bool isUserSpecified;
                    decimal finalValue;

                    if (slopeCode == "0")
                    {
                        // Code "0" means user-specified - always use user's value
                        isUserSpecified = true;
                        finalValue = userValue; // Use the value provided by user
                        _logger.LogInformation("DEBUG: PUT Slope Code=0 (User-Specified) - Using UserValue: {FinalValue}", finalValue);
                    }
                    else
                    {
                        // All other codes - always use resource default values (ignore user input)
                        isUserSpecified = false;
                        finalValue = ceilingSlope.Value; // Always use resource default value
                        _logger.LogInformation("DEBUG: PUT Slope Code={Code} - Using Resource Default Value: {FinalValue}",
                            slopeCode, finalValue);
                    }

                    ceiling.Measurements.Slope = new CeilingSlope
                    {
                        Code = slopeCode,
                        EnglishText = ceilingSlope.EnglishText,
                        FrenchText = ceilingSlope.FrenchText,
                        Value = finalValue,
                        IsUserSpecified = isUserSpecified
                    };

                    // Update DTO to return populated values
                    ceilingDto.Measurements.Slope.EnglishText = ceilingSlope.EnglishText;
                    ceilingDto.Measurements.Slope.FrenchText = ceilingSlope.FrenchText;
                    ceilingDto.Measurements.Slope.Value = finalValue;
                    ceilingDto.Measurements.Slope.IsUserSpecified = isUserSpecified;

                    _logger.LogInformation("DEBUG: PUT Slope Final Result - Code: {Code}, Value: {Value}, IsUserSpecified: {IsUserSpecified}",
                        slopeCode, finalValue, isUserSpecified);
                }
        
                var updatedCeiling = await _ceilingService.UpdateCeilingAsync(ceiling);
        
                if (updatedCeiling == null)
                {
                    _logger.LogWarning("Ceiling with ID: {CeilingId} not found for update", id);
                    return NotFound();
                }
        
                var updatedCeilingDto = _mapper.Map<CeilingDto>(updatedCeiling);
        
                return Ok(updatedCeilingDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating ceiling with ID: {CeilingId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while updating the ceiling");
            }
        }

        // DELETE: api/envelope/ceilings/{id}
        [HttpDelete("ceilings/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteCeiling(Guid id)
        {
            try
            {
                _logger.LogInformation("Deleting ceiling with ID: {CeilingId}", id);
        
                var result = await _ceilingService.DeleteCeilingAsync(id);
        
                if (!result)
                {
                    _logger.LogWarning("Ceiling with ID: {CeilingId} not found for deletion", id);
                    return NotFound();
                }
        
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting ceiling with ID: {CeilingId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while deleting the ceiling");
            }
        }

        // NEW: Get available ceiling types
        [HttpGet("ceilings/types")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<List<CeilingTypeDto>> GetCeilingTypes()
        {
            _logger.LogInformation("Getting all available ceiling types");
            
            var types = CeilingType.All;
            var typeDtos = _mapper.Map<List<CeilingTypeDto>>(types);
            
            return Ok(typeDtos);
        }

        // NEW: Get available ceiling slopes
        [HttpGet("ceilings/slopes")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<List<CeilingSlopeDto>> GetCeilingSlopes()
        {
            _logger.LogInformation("Getting all available ceiling slopes");
            
            var slopes = CeilingSlope.All;
            var slopeDtos = _mapper.Map<List<CeilingSlopeDto>>(slopes);
            
            return Ok(slopeDtos);
        }

        // GET: api/envelope/{houseId}/floors
        [HttpGet("{houseId}/floors")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<List<FloorDto>>> GetFloors(Guid houseId)
        {
            _logger.LogInformation("Getting all floors for house with ID: {HouseId}", houseId);

            var floors = await _floorService.GetFloorsByHouseIdAsync(houseId);

            if (floors == null || !floors.Any())
            {
                _logger.LogWarning("No floors found for house with ID: {HouseId}", houseId);
                return NotFound();
            }

            var floorDtos = _mapper.Map<List<FloorDto>>(floors);

            return Ok(floorDtos);
        }
        
        // GET: api/envelope/floors/{id}
        [HttpGet("floors/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<FloorDto>> GetFloorById(Guid id)
        {
            _logger.LogInformation("Getting floor with ID: {FloorId}", id);
            
            var floor = await _floorService.GetFloorByIdAsync(id);
            
            if (floor == null)
            {
                _logger.LogWarning("Floor with ID: {FloorId} not found", id);
                return NotFound();
            }
            
            var floorDto = _mapper.Map<FloorDto>(floor);
            
            return Ok(floorDto);
        }
        
        // POST: api/envelope/floors
        [HttpPost("floors")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<FloorDto>> AddFloor([FromBody] FloorDto floorDto)
        {
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid floor data submitted");
                return BadRequest(ModelState);
            }
            
            try
            {
                _logger.LogInformation("Adding new floor for house with ID: {HouseId}", floorDto.HouseId);
                
                var floor = _mapper.Map<Floor>(floorDto);
                if (floor.Id == Guid.Empty)
                {
                    floor.Id = Guid.NewGuid();
                }
                var createdFloor = await _floorService.AddFloorAsync(floor);
                var createdFloorDto = _mapper.Map<FloorDto>(createdFloor);
                
                return CreatedAtAction(nameof(GetFloorById), new { id = createdFloor.Id }, createdFloorDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding floor for house with ID: {HouseId}", floorDto.HouseId);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while adding the floor");
            }
        }
        
        // PUT: api/envelope/floors/{id}
        [HttpPut("floors/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<FloorDto>> UpdateFloor(Guid id, [FromBody] FloorDto floorDto)
        {
            if (id != floorDto.Id)
            {
                _logger.LogWarning("ID mismatch in update request. Path ID: {PathId}, DTO ID: {DtoId}", id, floorDto.Id);
                return BadRequest("ID in the path does not match ID in the request body");
            }
            
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid floor data submitted for update");
                return BadRequest(ModelState);
            }
            
            try
            {
                _logger.LogInformation("Updating floor with ID: {FloorId}", id);
                
                var floor = _mapper.Map<Floor>(floorDto);
                var updatedFloor = await _floorService.UpdateFloorAsync(floor);
                
                if (updatedFloor == null)
                {
                    _logger.LogWarning("Floor with ID: {FloorId} not found for update", id);
                    return NotFound();
                }
                
                var updatedFloorDto = _mapper.Map<FloorDto>(updatedFloor);
                
                return Ok(updatedFloorDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating floor with ID: {FloorId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while updating the floor");
            }
        }
        
        // DELETE: api/envelope/floors/{id}
        [HttpDelete("floors/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteFloor(Guid id)
        {
            _logger.LogInformation("Deleting floor with ID: {FloorId}", id);
            
            var result = await _floorService.DeleteFloorAsync(id);
            
            if (!result)
            {
                _logger.LogWarning("Floor with ID: {FloorId} not found for deletion", id);
                return NotFound();
            }
            
            return NoContent();
        }
    
        // GET: api/envelope/{houseId}/thermal-performance
        [HttpGet("{houseId}/thermal-performance")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<dynamic>> GetThermalPerformance(Guid houseId)
        {
            _logger.LogInformation("Getting thermal performance for house with ID: {HouseId}", houseId);
            
            var thermalPerformance = await _thermalPerformanceService.GetThermalPerformanceAsync(houseId);
            
            if (thermalPerformance == null)
            {
                _logger.LogWarning("No thermal performance data found for house with ID: {HouseId}", houseId);
                return NotFound();
            }
            
            return Ok(thermalPerformance);
        }

        // DOOR ENDPOINTS

        // GET: api/envelope/{houseId}/doors
        [HttpGet("{houseId}/doors")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<List<DoorDto>>> GetDoorsByHouseId(Guid houseId)
        {
            _logger.LogInformation("Getting doors for house with ID: {HouseId}", houseId);

            var doors = await _doorService.GetDoorsByHouseIdAsync(houseId);
            var doorDtos = _mapper.Map<List<DoorDto>>(doors);

            return Ok(doorDtos);
        }

        // GET: api/envelope/doors/{id}
        [HttpGet("doors/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<DoorDto>> GetDoorById(Guid id)
        {
            _logger.LogInformation("Getting door with ID: {DoorId}", id);

            var door = await _doorService.GetDoorByIdAsync(id);

            if (door == null)
            {
                _logger.LogWarning("Door with ID: {DoorId} not found", id);
                return NotFound();
            }

            var doorDto = _mapper.Map<DoorDto>(door);
            return Ok(doorDto);
        }

        // GET: api/envelope/doors
        [HttpGet("doors")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<DoorDto>>> GetAllDoors()
        {
            _logger.LogInformation("Getting all doors");

            var doors = await _doorService.GetAllDoorsAsync();
            var doorDtos = _mapper.Map<List<DoorDto>>(doors);

            return Ok(doorDtos);
        }

        // POST: api/envelope/doors
        [HttpPost("doors")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<DoorDto>> CreateDoor([FromBody] DoorDto doorDto)
        {
            _logger.LogInformation("Creating new door for house with ID: {HouseId}", doorDto.HouseId);

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var door = _mapper.Map<Door>(doorDto);
            var createdDoor = await _doorService.AddDoorAsync(door);
            var createdDoorDto = _mapper.Map<DoorDto>(createdDoor);

            return CreatedAtAction(nameof(GetDoorById), new { id = createdDoorDto.Id }, createdDoorDto);
        }

        // PUT: api/envelope/doors/{id}
        [HttpPut("doors/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<DoorDto>> UpdateDoor(Guid id, [FromBody] DoorDto doorDto)
        {
            _logger.LogInformation("Updating door with ID: {DoorId}", id);

            if (id != doorDto.Id)
            {
                return BadRequest("ID mismatch");
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var door = _mapper.Map<Door>(doorDto);
            var updatedDoor = await _doorService.UpdateDoorAsync(door);

            if (updatedDoor == null)
            {
                _logger.LogWarning("Door with ID: {DoorId} not found for update", id);
                return NotFound();
            }

            var updatedDoorDto = _mapper.Map<DoorDto>(updatedDoor);
            return Ok(updatedDoorDto);
        }

        // DELETE: api/envelope/doors/{id}
        [HttpDelete("doors/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteDoor(Guid id)
        {
            _logger.LogInformation("Deleting door with ID: {DoorId}", id);

            try
            {
                await _doorService.DeleteDoorAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning("Door with ID: {DoorId} not found for deletion", id);
                return NotFound();
            }
        }

        // WINDOW ENDPOINTS

        // GET: api/envelope/{houseId}/windows
        [HttpGet("{houseId}/windows")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<List<WindowDto>>> GetWindowsByHouseId(Guid houseId)
        {
            _logger.LogInformation("Getting windows for house with ID: {HouseId}", houseId);

            var windows = await _windowService.GetWindowsByHouseIdAsync(houseId);
            var windowDtos = _mapper.Map<List<WindowDto>>(windows);

            return Ok(windowDtos);
        }

        // GET: api/envelope/windows/{id}
        [HttpGet("windows/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<WindowDto>> GetWindowById(Guid id)
        {
            _logger.LogInformation("Getting window with ID: {WindowId}", id);

            var window = await _windowService.GetWindowByIdAsync(id);

            if (window == null)
            {
                _logger.LogWarning("Window with ID: {WindowId} not found", id);
                return NotFound();
            }

            var windowDto = _mapper.Map<WindowDto>(window);
            return Ok(windowDto);
        }

        // GET: api/envelope/windows
        [HttpGet("windows")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<WindowDto>>> GetAllWindows()
        {
            _logger.LogInformation("Getting all windows");

            var windows = await _windowService.GetAllWindowsAsync();
            var windowDtos = _mapper.Map<List<WindowDto>>(windows);

            return Ok(windowDtos);
        }

        // POST: api/envelope/windows
        [HttpPost("windows")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<WindowDto>> CreateWindow([FromBody] WindowDto windowDto)
        {
            _logger.LogInformation("Creating new window for house with ID: {HouseId}", windowDto.HouseId);

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var window = _mapper.Map<Window>(windowDto);
            var createdWindow = await _windowService.AddWindowAsync(window);
            var createdWindowDto = _mapper.Map<WindowDto>(createdWindow);

            return CreatedAtAction(nameof(GetWindowById), new { id = createdWindowDto.Id }, createdWindowDto);
        }

        // PUT: api/envelope/windows/{id}
        [HttpPut("windows/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<WindowDto>> UpdateWindow(Guid id, [FromBody] WindowDto windowDto)
        {
            _logger.LogInformation("Updating window with ID: {WindowId}", id);

            if (id != windowDto.Id)
            {
                return BadRequest("ID mismatch");
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var window = _mapper.Map<Window>(windowDto);
            var updatedWindow = await _windowService.UpdateWindowAsync(window);

            if (updatedWindow == null)
            {
                _logger.LogWarning("Window with ID: {WindowId} not found for update", id);
                return NotFound();
            }

            var updatedWindowDto = _mapper.Map<WindowDto>(updatedWindow);
            return Ok(updatedWindowDto);
        }

        // DELETE: api/envelope/windows/{id}
        [HttpDelete("windows/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteWindow(Guid id)
        {
            _logger.LogInformation("Deleting window with ID: {WindowId}", id);

            try
            {
                await _windowService.DeleteWindowAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning("Window with ID: {WindowId} not found for deletion", id);
                return NotFound();
            }
        }

        // ===== FLOOR HEADER ENDPOINTS =====

        // GET: api/envelope/{houseId}/floorheaders
        [HttpGet("{houseId}/floorheaders")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<List<FloorHeaderDto>>> GetFloorHeadersByHouseId(Guid houseId)
        {
            _logger.LogInformation("Getting floor headers for house with ID: {HouseId}", houseId);

            var floorHeaders = await _floorHeaderService.GetFloorHeadersByHouseIdAsync(houseId);
            var floorHeaderDtos = _mapper.Map<List<FloorHeaderDto>>(floorHeaders);

            return Ok(floorHeaderDtos);
        }

        // GET: api/envelope/floorheaders/{id}
        [HttpGet("floorheaders/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<FloorHeaderDto>> GetFloorHeaderById(Guid id)
        {
            _logger.LogInformation("Getting floor header with ID: {FloorHeaderId}", id);

            var floorHeader = await _floorHeaderService.GetFloorHeaderByIdAsync(id);
            if (floorHeader == null)
            {
                _logger.LogWarning("Floor header with ID: {FloorHeaderId} not found", id);
                return NotFound();
            }

            var floorHeaderDto = _mapper.Map<FloorHeaderDto>(floorHeader);
            return Ok(floorHeaderDto);
        }

        // POST: api/envelope/floorheaders
        [HttpPost("floorheaders")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<FloorHeaderDto>> CreateFloorHeader([FromBody] FloorHeaderDto floorHeaderDto)
        {
            _logger.LogInformation("Creating new floor header for house with ID: {HouseId}", floorHeaderDto.HouseId);

            // Handle FacingDirection - Auto-populate text fields if only code provided
            if (!string.IsNullOrEmpty(floorHeaderDto.FacingDirection?.Code) &&
                string.IsNullOrEmpty(floorHeaderDto.FacingDirection?.English))
            {
                var directionCode = floorHeaderDto.FacingDirection.Code;
                var isUserSpecified = floorHeaderDto.FacingDirection.IsUserSpecified;

                // Find the direction by code and preserve IsUserSpecified value
                var direction = FloorHeaderDirections.All.FirstOrDefault(d => d.Code == directionCode) ?? FloorHeaderDirections.NotApplicable;

                floorHeaderDto.FacingDirection.English = direction.English;
                floorHeaderDto.FacingDirection.French = direction.French;
                // Keep the IsUserSpecified value as provided by the user
            }

            var floorHeader = _mapper.Map<FloorHeader>(floorHeaderDto);
            var createdFloorHeader = await _floorHeaderService.CreateFloorHeaderAsync(floorHeader);
            var createdFloorHeaderDto = _mapper.Map<FloorHeaderDto>(createdFloorHeader);

            return CreatedAtAction(nameof(GetFloorHeaderById), new { id = createdFloorHeaderDto.Id }, createdFloorHeaderDto);
        }

        // PUT: api/envelope/floorheaders/{id}
        [HttpPut("floorheaders/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<FloorHeaderDto>> UpdateFloorHeader(Guid id, [FromBody] FloorHeaderDto floorHeaderDto)
        {
            _logger.LogInformation("Updating floor header with ID: {FloorHeaderId}", id);

            if (id != floorHeaderDto.Id)
            {
                _logger.LogWarning("ID mismatch: {Id} != {DtoId}", id, floorHeaderDto.Id);
                return BadRequest("ID mismatch");
            }

            // Handle FacingDirection - Auto-populate text fields if only code provided
            if (!string.IsNullOrEmpty(floorHeaderDto.FacingDirection?.Code) &&
                string.IsNullOrEmpty(floorHeaderDto.FacingDirection?.English))
            {
                var directionCode = floorHeaderDto.FacingDirection.Code;
                var isUserSpecified = floorHeaderDto.FacingDirection.IsUserSpecified;

                // Find the direction by code and preserve IsUserSpecified value
                var direction = FloorHeaderDirections.All.FirstOrDefault(d => d.Code == directionCode) ?? FloorHeaderDirections.NotApplicable;

                floorHeaderDto.FacingDirection.English = direction.English;
                floorHeaderDto.FacingDirection.French = direction.French;
                // Keep the IsUserSpecified value as provided by the user
            }

            var floorHeader = _mapper.Map<FloorHeader>(floorHeaderDto);
            var updatedFloorHeader = await _floorHeaderService.UpdateFloorHeaderAsync(floorHeader);

            var updatedFloorHeaderDto = _mapper.Map<FloorHeaderDto>(updatedFloorHeader);
            return Ok(updatedFloorHeaderDto);
        }

        // DELETE: api/envelope/floorheaders/{id}
        [HttpDelete("floorheaders/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteFloorHeader(Guid id)
        {
            _logger.LogInformation("Deleting floor header with ID: {FloorHeaderId}", id);

            try
            {
                await _floorHeaderService.DeleteFloorHeaderAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning("Floor header with ID: {FloorHeaderId} not found for deletion", id);
                return NotFound();
            }
        }

        // ROOM ENDPOINTS

        // GET: api/envelope/{houseId}/rooms
        [HttpGet("{houseId}/rooms")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<List<RoomDto>>> GetRoomsByHouseId(Guid houseId)
        {
            _logger.LogInformation("Getting rooms for house with ID: {HouseId}", houseId);

            var rooms = await _roomService.GetRoomsByHouseIdAsync(houseId);
            var roomDtos = _mapper.Map<List<RoomDto>>(rooms);

            return Ok(roomDtos);
        }

        // GET: api/envelope/rooms/{id}
        [HttpGet("rooms/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<RoomDto>> GetRoomById(Guid id)
        {
            _logger.LogInformation("Getting room with ID: {RoomId}", id);

            var room = await _roomService.GetRoomByIdAsync(id);
            if (room == null)
            {
                _logger.LogWarning("Room with ID: {RoomId} not found", id);
                return NotFound();
            }

            var roomDto = _mapper.Map<RoomDto>(room);
            return Ok(roomDto);
        }

        // GET: api/envelope/rooms
        [HttpGet("rooms")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<RoomDto>>> GetAllRooms()
        {
            _logger.LogInformation("Getting all rooms");

            var rooms = await _roomService.GetAllRoomsAsync();
            var roomDtos = _mapper.Map<List<RoomDto>>(rooms);

            return Ok(roomDtos);
        }

        // POST: api/envelope/rooms
        [HttpPost("rooms")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<RoomDto>> CreateRoom([FromBody] RoomDto roomDto)
        {
            _logger.LogInformation("Creating new room for house with ID: {HouseId}", roomDto.HouseId);

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var room = _mapper.Map<Room>(roomDto);
            var createdRoom = await _roomService.AddRoomAsync(room);
            var createdRoomDto = _mapper.Map<RoomDto>(createdRoom);

            return CreatedAtAction(nameof(GetRoomById), new { id = createdRoomDto.Id }, createdRoomDto);
        }

        // PUT: api/envelope/rooms/{id}
        [HttpPut("rooms/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<RoomDto>> UpdateRoom(Guid id, [FromBody] RoomDto roomDto)
        {
            _logger.LogInformation("Updating room with ID: {RoomId}", id);

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (id != roomDto.Id)
            {
                return BadRequest("ID mismatch");
            }

            var room = _mapper.Map<Room>(roomDto);
            var updatedRoom = await _roomService.UpdateRoomAsync(room);

            if (updatedRoom == null)
            {
                _logger.LogWarning("Room with ID: {RoomId} not found for update", id);
                return NotFound();
            }

            var updatedRoomDto = _mapper.Map<RoomDto>(updatedRoom);
            return Ok(updatedRoomDto);
        }

        // DELETE: api/envelope/rooms/{id}
        [HttpDelete("rooms/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteRoom(Guid id)
        {
            _logger.LogInformation("Deleting room with ID: {RoomId}", id);

            try
            {
                await _roomService.DeleteRoomAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning("Room with ID: {RoomId} not found for deletion", id);
                return NotFound();
            }
        }
    }
}