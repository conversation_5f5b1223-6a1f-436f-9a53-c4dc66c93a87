using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EnvelopeService.Core.Interfaces;
using EnvelopeService.Core.Models;
using EnvelopeService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace EnvelopeService.Infrastructure.Repositories
{
    public class RoomRepository : IRoomRepository
    {
        private readonly EnvelopDbContext _context;

        public RoomRepository(EnvelopDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<List<Room>> GetRoomsByHouseIdAsync(Guid houseId)
        {
            var rooms = await _context.Rooms
                .Include(r => r.Construction)
                .Include(r => r.Measurements)
                .Where(r => r.HouseId == houseId)
                .ToListAsync();

            // Populate resource text values based on codes
            foreach (var room in rooms)
            {
                PopulateResourceTextValues(room);
            }

            return rooms;
        }

        public async Task<Room> GetRoomByIdAsync(Guid id)
        {
            var room = await _context.Rooms
                .Include(r => r.Construction)
                .Include(r => r.Measurements)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (room != null)
            {
                PopulateResourceTextValues(room);
            }

            return room;
        }

        public async Task<List<Room>> GetAllRoomsAsync()
        {
            var rooms = await _context.Rooms
                .Include(r => r.Construction)
                .Include(r => r.Measurements)
                .ToListAsync();

            // Populate resource text values based on codes
            foreach (var room in rooms)
            {
                PopulateResourceTextValues(room);
            }

            return rooms;
        }

        public async Task<Room> AddRoomAsync(Room room)
        {
            // Set IDs for the room and related entities
            if (room.Id == Guid.Empty)
            {
                room.Id = Guid.NewGuid();
            }

            // Handle RoomConstruction
            if (room.Construction != null)
            {
                if (room.Construction.Id == Guid.Empty)
                {
                    room.Construction.Id = Guid.NewGuid();
                }
                room.Construction.RoomId = room.Id;
            }

            // Handle RoomMeasurements
            if (room.Measurements != null)
            {
                if (room.Measurements.Id == Guid.Empty)
                {
                    room.Measurements.Id = Guid.NewGuid();
                }
                room.Measurements.RoomId = room.Id;
            }

            _context.Rooms.Add(room);
            await _context.SaveChangesAsync();

            return room;
        }

        public async Task<Room> UpdateRoomAsync(Room room)
        {
            var existingRoom = await _context.Rooms
                .Include(r => r.Construction)
                .Include(r => r.Measurements)
                .FirstOrDefaultAsync(r => r.Id == room.Id);

            if (existingRoom == null)
            {
                throw new ArgumentException($"Room with ID {room.Id} not found.");
            }

            // Update room properties
            existingRoom.HouseId = room.HouseId;
            existingRoom.Label = room.Label;

            // Update construction
            if (room.Construction != null)
            {
                if (existingRoom.Construction == null)
                {
                    existingRoom.Construction = new RoomConstruction();
                    existingRoom.Construction.Id = Guid.NewGuid();
                    existingRoom.Construction.RoomId = existingRoom.Id;
                }

                existingRoom.Construction.Type = room.Construction.Type;
                existingRoom.Construction.Floor = room.Construction.Floor;
                existingRoom.Construction.FoundationBelow = room.Construction.FoundationBelow;
            }

            // Update measurements
            if (room.Measurements != null)
            {
                if (existingRoom.Measurements == null)
                {
                    existingRoom.Measurements = new RoomMeasurements();
                    existingRoom.Measurements.Id = Guid.NewGuid();
                    existingRoom.Measurements.RoomId = existingRoom.Id;
                }

                existingRoom.Measurements.IsRectangular = room.Measurements.IsRectangular;
                existingRoom.Measurements.Height = room.Measurements.Height;
                existingRoom.Measurements.Width = room.Measurements.Width;
                existingRoom.Measurements.Depth = room.Measurements.Depth;
                existingRoom.Measurements.Perimeter = room.Measurements.Perimeter;
                existingRoom.Measurements.Area = room.Measurements.Area;
            }

            await _context.SaveChangesAsync();

            return existingRoom;
        }

        public async Task DeleteRoomAsync(Guid id)
        {
            var room = await _context.Rooms.FindAsync(id);
            if (room != null)
            {
                _context.Rooms.Remove(room);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.Rooms.AnyAsync(r => r.Id == id);
        }

        private void PopulateResourceTextValues(Room room)
        {
            if (room?.Construction != null)
            {
                // Populate RoomType text values
                if (room.Construction.Type != null && !string.IsNullOrEmpty(room.Construction.Type.Code))
                {
                    var roomType = GetRoomTypeByCode(room.Construction.Type.Code);
                    if (roomType != null)
                    {
                        room.Construction.Type = new RoomTypes
                        {
                            Code = room.Construction.Type.Code,
                            English = roomType.English,
                            French = roomType.French,
                            IsUserSpecified = room.Construction.Type.IsUserSpecified
                        };
                    }
                }

                // Populate RoomFloor text values
                if (room.Construction.Floor != null && !string.IsNullOrEmpty(room.Construction.Floor.Code))
                {
                    var roomFloor = GetRoomFloorByCode(room.Construction.Floor.Code);
                    if (roomFloor != null)
                    {
                        room.Construction.Floor = new RoomFloors
                        {
                            Code = room.Construction.Floor.Code,
                            English = roomFloor.English,
                            French = roomFloor.French,
                            IsUserSpecified = room.Construction.Floor.IsUserSpecified
                        };
                    }
                }
            }
        }

        private RoomTypes GetRoomTypeByCode(string code)
        {
            return RoomTypes.All.FirstOrDefault(rt => rt.Code == code);
        }

        private RoomFloors GetRoomFloorByCode(string code)
        {
            return RoomFloors.All.FirstOrDefault(rf => rf.Code == code);
        }
    }
}
